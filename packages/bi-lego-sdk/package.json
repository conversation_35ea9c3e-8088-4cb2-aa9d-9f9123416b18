{"version": "1.0.0", "name": "@blm/bi-lego-sdk", "author": "<<EMAIL>>", "description": "乐高SDK", "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/types/index.d.ts", "files": ["dist/"], "scripts": {"build": "cross-env NODE_ENV=production modern build", "dev": "storybook dev -p 8848"}, "exports": {"./*": "./*", ".": {"import": "./es/index.js", "require": "./lib/index.js"}, "./utils": "./dist/es/utils.js", "./LegoEditView": "./dist/es/LegoEditView.js", "./LegoListView": "./dist/es/LegoListView.js", "./LegoPermissionView": "./dist/es/LegoPermissionView.js", "./LegoRenderView": "./dist/es/LegoRenderView.js", "./LegoRender": "./dist/es/LegoRender.js", "./LegoExportRecords": "./dist/es/LegoExportRecords.js", "./LegoPublishExport": "./dist/es/LegoPublishExport.js"}, "typesVersions": {"*": {".": ["./dist/types/index.d.ts"], "utils": ["./dist/types/utils.d.ts"], "LegoEditView": ["./dist/types/LegoEditView.d.ts"], "LegoListView": ["./dist/types/LegoListView.d.ts"], "LegoPermissionView": ["./dist/types/LegoPermissionView.d.ts"], "LegoRenderView": ["./dist/types/LegoRenderView.d.ts"], "LegoRender": ["./dist/types/LegoRender.d.ts"], "LegoExportRecords": ["./dist/types/LegoExportRecords.d.ts"], "LegoPublishExport": ["./dist/types/LegoPublishExport.d.ts"]}}, "dependencies": {"@alilc/lowcode-plugin-inject": "^1.2.3", "@alilc/lowcode-react-renderer": "^1.2.5", "@alilc/lowcode-types": "^1.2.2", "@alilc/lowcode-utils": "^1.2.3", "@ant-design/icons": "^5.2.6", "@blmcp/peento-businessComponents": "0.0.63", "@blmcp/ui": "1.1.19", "@blmlc/lego-init": "^1.0.8-beta.30", "@codemirror/autocomplete": "^6.16.0", "@codemirror/commands": "^6.6.0", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.3", "@ice/stark-app": "^1.5.0", "ahooks": "^3.7.8", "antd": "^5.8.1", "axios": "^1.6.2", "blm-utils": "1.2.88-rc.4", "classnames": "^2.5.0", "dayjs": "1.11.9", "echarts": "^5.6.0", "js-cookie": "^2.2.1", "lodash-es": "4.17.21", "numeral": "^2.0.6", "prop-types": "^15.6.0", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-joyride": "2.7.2", "resize-observer-polyfill": "^1.5.1"}, "devDependencies": {"@babel/parser": "^7.23.6", "@babel/runtime": "^7.0.0", "@modern-js/module-tools": "^2.58.1", "@modern-js/storybook": "2.53.0", "@storybook/addon-essentials": "7", "@svgr/webpack": "^5.5.0", "@types/react": "^18.0.25", "click-to-react-component": "1.1.0", "cross-env": "^7.0.3", "http-proxy-middleware": "^3.0.5", "stylelint": "^15.10.3", "typescript": "^5.5.4"}}