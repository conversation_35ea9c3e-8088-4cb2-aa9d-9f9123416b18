import { useTemplateConfig } from '@/tool/config';
import { Spin, Empty, message } from '@blmcp/ui';
import { forwardRef, useEffect } from 'react';
import './dependent.ts';
import './style.less';
import event from '@/tool/event';

export default function (Component: JSX.ElementType) {
  return forwardRef((props, ref) => {
    const [state] = useTemplateConfig('state');

    useEffect(() => {
      return () => {
        // @ts-expect-error
        if (props.unEvent !== false) {
          event.off();
        }
      };
    }, []);

    if (state === 'error')
      return (
        <Empty
          style={{ marginTop: '100px' }}
          emptytype="moduleNotData"
          description="获取数据失败，请刷新重试"
        />
      );

    if (state !== 'loaded') {
      return <Spin size="large" className="lego-page-loading" />;
    }
    return <Component {...props} ref={ref} />;
  });
}
