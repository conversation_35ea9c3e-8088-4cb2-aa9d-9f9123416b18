import {
  useState,
  useLayoutEffect,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import { getWindow } from '@/utils/common';

const stateKey = '__lego_bi_state__';
const methodKey = '__lego_bi_methods__';

declare global {
  interface Window {
    [stateKey]: any;
    [methodKey]: any;
    SET_BASE_WINDOW: (key: string, value: any) => void;
  }
}

const Window = getWindow();

const setWindow = function (key: string, value: any) {
  if (Window.SET_BASE_WINDOW) {
    Window.SET_BASE_WINDOW(key, value);
  } else {
    Window[key as any] = value;
  }
  return value;
};

const state: { [key: string]: any } =
  Window[stateKey] || setWindow(stateKey, {});
const methods: { [key: string]: any } =
  Window[methodKey] || setWindow(methodKey, {});

export default class Store<T> {
  namespace: string;
  state: { [key: string]: T };
  methods: { [key: string]: Dispatch<SetStateAction<T | undefined>>[] | null };
  constructor(namespace: string, init?: any) {
    this.namespace = namespace;
    this.state = state[namespace] = state[namespace] || init || {};
    this.methods = methods[namespace] = methods[namespace] || {};
    // this.clear();
  }
  use(key: string, fun: Dispatch<SetStateAction<T | undefined>>) {
    this.methods[key] = this.methods[key] || [];

    this.methods[key]?.push(fun);

    return () => {
      const findIndex = this.methods[key]?.findIndex((v) => v === fun);
      this.methods[key]?.splice(findIndex as number, 1);
      // delete this.methods[key]
      // if (!this.methods[key]?.length) {
      //   delete this.methods[key];
      //   delete this.state[key];
      // }
    };
  }
  set(key: string, value: T) {
    this.attr(key, value);
    if (this.methods[key]) {
      this.methods[key]?.forEach((fn) => {
        fn(value);
      });
    }
  }
  merge(key: string, value: { [key: string]: any }) {
    const op: any = this.get(key) || {};
    // const _op = JSON.parse(JSON.stringify(op));
    let diff = false;
    for (let k in value) {
      if (value[k] !== op[k]) {
        op[k] = value[k];
        diff = true;
      }
    }

    if (diff) {
      this.set(key, { ...op });
    }
  }
  get(key: string) {
    return this.state[key];
  }
  has(key: string) {
    return !!this.state[key];
  }
  attr(key: string, value: T) {
    this.state[key] = value;
  }
  delete(key: string) {
    delete this.state[key];
    delete this.methods[key];
    delete state[this.namespace][key];
    delete methods[this.namespace][key];
  }
  conditionDelete(key: string, value: boolean) {
    for (let k in this.state) {
      if (!this.state.hasOwnProperty(k) || this.state[k][key] !== value)
        continue;
      this.delete(k);
    }
  }
  clear() {
    for (let k in this.state) {
      if (!this.state.hasOwnProperty(k)) continue;
      this.delete(k);
    }
  }
  all() {
    return Object.entries(this.state);
  }

  keys() {
    return Object.keys(this.state);
  }

  setAll(value: T) {
    this.keys().forEach((key) => {
      this.set(key, value);
    });
  }
  mergeAll(value: { [key: string]: any }) {
    this.keys().forEach((key) => {
      this.merge(key, value);
    });
  }
}

export type FunType<T> = {
  (value: T): void;
  (value: { [key: string]: any }, bool: boolean): void;
};

export function useStore<T>(
  context: Store<T>,
  key: string,
  init?: T,
): [T, FunType<T>] {
  // if (key === undefined) return [undefined as T, () => {}];

  const [data, setData] = useState(context.get(key) ?? init);
  useLayoutEffect(() => {
    return context.use(key, setData);
  }, []);

  useEffect(() => {
    if (context.get(key) !== data) {
      setData(context.get(key) ?? init);
    }
  }, []);

  return [
    data as T,
    function (value: any, merge?: boolean) {
      if (merge) {
        context.merge(key, value);
      } else {
        context.set(key, value);
      }
    },
  ];
}

export type DISPATCH<T> = (state: T, action: any) => T;
export interface CreateContext<T> {
  callback: DISPATCH<T>;
  initValue: T;
  context: Store<T>;
  useKey: string;
}

let createNum = 0;
export function createContext<T>(
  callback: DISPATCH<T>,
  initValue: T,
): CreateContext<T> {
  createNum += 1;
  const useKey = 'createContext' + createNum;
  const context = new Store<T>('useDispatch');

  return {
    callback,
    initValue,
    context,
    useKey,
  };
}

export function useDispatch<T>({
  useKey,
  initValue,
  context,
  callback,
}: CreateContext<T>): [T, (option: any) => void] {
  const [data, setData] = useState(initValue);

  useLayoutEffect(() => {
    return context.use(useKey, setData as any);
  }, []);

  return [
    data as T,
    function (option: any) {
      const store = context.state[useKey] || initValue;
      context.set(useKey, callback(store, option));
    },
  ];
}
