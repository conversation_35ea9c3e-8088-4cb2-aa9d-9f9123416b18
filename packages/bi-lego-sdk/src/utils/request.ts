/*
 * @Description: 请输入....
 * @Author: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 * @Date: 2025-06-06 16:09:51
 * @LastEditTime: 2025-06-13 19:44:23
 * @LastEditors: g<PERSON><PERSON><PERSON><PERSON>@bailongma-inc.com
 */
// @ts-nocheck
// import { createRequest } from '@blmcp/peento-request';
import { message } from '@blmcp/ui';
// import { bosProxyRequests, bosProxyResponses } from 'blm-utils';
import { apiProxy } from '@/utils/apiProxy';
import config from '../tool/config';

// const request = createRequest({
//   onRefreshToken: () => {},
//   onLogout: () => {},
//   responseNoReject: true,
// });

// request.interceptors.request.use(
//   (config: any) => {
//     // 哈勃环境下 替换url
//     const proxyConfig = apiProxy(config);
//     return proxyConfig;
//   },
//   (error) => Promise.reject(error),
// );

// request.interceptors.response.use(
//   (response) => {
//     if (response.config) {
//       if (typeof response.data === 'object' && response.data.code !== 1) {
//         // @ts-expect-error
//         if (!response.config.notMessage) {
//           const msg = response.data.tips || response.data.msg;
//           message.error(msg);
//         }
//         return Promise.reject(response.data);
//       }
//     }
//     return response;
//   },
//   (error) => {
//     const msg = typeof error === 'object' ? error.msg : error;
//     // 增加全局错误提示， 入参中包含error_code的场景，返回的错误信息为对象，在业务中单独处理
//     if (typeof msg === 'string') {
//       if (msg === '请勿重复请求') return Promise.reject(msg);
//       if (msg.includes('请求超时')) {
//         message.error('接口请求超时');
//       } else {
//         message.error(error);
//       }
//     }
//     return Promise.reject(error);
//   },
// );

// const Request = ()  =>{
//   const configRequest = config.get('request') || null;
//   if (configRequest) {
//     return configRequest;
//   } else {
//     return request
//   }
// };

// Request.prototype = request.prototype;
// for(const key in request) {
//   Request[key] = request[key];
// }
// const Request = (config) => {
//   const configRequest = config.get('request') || null;
//   if (configRequest) {
//     Request.prototype = configRequest.prototype;
//     for (const key in configRequest) {
//       Request[key] = configRequest[key];
//     }
//     return configRequest(config);
//   } else {
//     Request.prototype = request.prototype;
//     for (const key in request) {
//       Request[key] = request[key];
//     }
//     return request(config);
//   }
// };

function axiosEntry(...rest) {
  const request = config.get('setting.request');

  if (!window.isUse) {
    window.isUse = true;
    request.interceptors.request.use(
      (config: any) => {
        // 哈勃环境下 替换url
        const proxyConfig = apiProxy(config);
        return proxyConfig;
      },
      (error) => Promise.reject(error),
    );

    axiosEntry.prototype = request.prototype;
    for (const key in request) {
      axiosEntry[key] = request[key];
    }
  }
  return request(...rest);
}

export default axiosEntry;
