import config from '../tool/config';

export function getWindow() {
  try {
    return window.top || window.parent || window.window || window;
  } catch (e) {
    return window;
  }
}

export function getIframe(alias: any, index = 0) {
  try {
    return window.frames?.[index]?.[alias];
  } catch (error) {
    return null;
  }
}

export function getVariable(key: string, frame = false) {
  const Window = getWindow();
  return (Window[key as any] || (frame && getIframe(key)) || null) as any;
}

export function setVariable(key: string, value: any) {
  const Window = getWindow();
  const Value = getVariable(key, true) || value;
  if (Window.SET_BASE_WINDOW) {
    Window.SET_BASE_WINDOW(key, Value);
  } else {
    Window[key as any] = Value;
  }
  return Value;
}

export const getLegoEnv = () => {
  const env = config.get('setting.env');
  return env || 'use';
};
export const isLegoDev = () => {
  return getLegoEnv() === 'dev';
};
export const isLegoUse = () => {
  return getLegoEnv() === 'use';
};
export const routerBase = () => {
  return isLegoDev()
    ? config.get('basicInfo.userBaseRouter')
    : config.get('basicInfo.manageBaseRouter');
};
export const cdnPrefix = () => {
  return config.get('setting.cdnPrefix') || config.get('basicInfo.cdnPrefix');
};
