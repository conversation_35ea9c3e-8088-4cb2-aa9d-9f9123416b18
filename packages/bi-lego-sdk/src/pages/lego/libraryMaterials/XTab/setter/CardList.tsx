import { TopDnDProvider } from '../../setter/TopDnDProvider';
import { Card } from './Card';

export const CardList = ({ value, deleteItem, inputChange, moveCard }) => {
  return (
    <TopDnDProvider>
      <div>
        {value?.map((item, index: number) => {
          return (
            <Card
              key={item.key}
              deleteItem={deleteItem(index)}
              inputChange={inputChange(index)}
              item={item}
              index={index}
              moveCard={moveCard}
            />
          );
        })}
      </div>
    </TopDnDProvider>
  );
};
