// @ts-nocheck
import { useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Input } from '@blmcp/ui';
import { message } from '@blmcp/ui';
import { ReactComponent as DeleIcon } from '@/assets/lego/delete-menu.svg';
import { ReactComponent as DragIcon } from '@/assets/lego/drag-item.svg';
import { isValidName } from '@/pages/lego/utils/validate';
import type { XYCoord } from 'dnd-core';
import type { FC } from 'react';
import styles from './index.module.less';

const dragType = 'TabSettingType';
interface CardProps {
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  dragType?: string;
  deleteItem: () => void;
  inputChange: (value: string) => void;
  item: { label: string; key: string };
}
interface DragItem {
  index: number;
  type: string;
}
let disabledDelete = false;
export const Card: FC<CardProps> = ({
  index,
  item,
  moveCard,

  inputChange,
  deleteItem,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [value, setValue] = useState(item.label);

  const inputValueChange = (e) => {
    setValue(e.target.value);
  };
  const inputBlur = () => {
    if (value.trim() === '') {
      setValue(item.label);
    } else if (value !== item.label) {
      inputChange(value);
    }
  };

  const [{}, drop] = useDrop<DragItem, void, Record<string, unknown>>({
    accept: dragType,
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: dragType,
    item: { index },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drop(ref);
  const opacity = isDragging ? 0 : 1;
  return (
    <div ref={preview}>
      <div ref={ref} style={{ opacity }}>
        <div style={{ display: 'inline-block' }} ref={drag}>
          <DragIcon className={styles['icon-drag']} />
        </div>

        <Input
          maxLength="50"
          value={value}
          onChange={inputValueChange}
          onBlur={inputBlur}
          className={styles['item-input']}
          placeholder="选项卡名称"
        />

        <i
          onClick={() => {
            if (!disabledDelete) {
              deleteItem();
              disabledDelete = true;
              setTimeout(() => {
                disabledDelete = false;
              }, 1000);
            }
          }}
        >
          <DeleIcon className={styles['icon']} />
        </i>
      </div>
    </div>
  );
};
