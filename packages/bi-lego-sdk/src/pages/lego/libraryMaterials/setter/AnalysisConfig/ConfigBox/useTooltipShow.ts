import { useEffect, useRef, useState } from 'react';

// tooltip 按需显示
export const useTooltipShow = (text: string, sortType = '', more = false) => {
  const [tooltipEnable, setTooltipEnable] = useState(false);
  const textRef = useRef<HTMLSpanElement>(null);

  // tooltipEnable 判断
  useEffect(() => {
    const scrollWidth = textRef?.current?.scrollWidth ?? 0;
    const offsetWidth = textRef?.current?.offsetWidth ?? 0;
    if (scrollWidth > offsetWidth) {
      setTooltipEnable(true);
    } else {
      setTooltipEnable(false);
    }
  }, [text, sortType, more]);

  return { tooltipEnable, textRef };
};
