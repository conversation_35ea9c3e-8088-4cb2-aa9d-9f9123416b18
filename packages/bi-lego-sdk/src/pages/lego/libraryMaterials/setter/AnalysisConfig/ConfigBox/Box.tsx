// @ts-nocheck
import { Dropdown, Tooltip } from '@blmcp/ui';
import { useCallback } from 'react';
import { ReactComponent as DragIcon } from '@/assets/lego/drag-icon.svg';
import { ReactComponent as MoreOutlinedIcon } from './moreOutlined.svg';
import { FieldType } from '../types';
import { Aggregate, AggregateType } from '../constant';
import { ItemTypes } from '../ItemTypes';
import { useTooltipShow } from './useTooltipShow';
import styles from './index.module.less';
import { sortIconMap } from './config';
import { Card } from './Card';
import { ColumnsType } from './';

interface BoxProps {
  col: ColumnsType;
  index: number;
  onClick: (index: number) => void;
  more: boolean;
  allowedDropEffect: string;
  getRootItems: (
    fieldType: FieldType,
    dataType: number,
    index: number,
    isAggr: boolean,
    summationComputeModeId: number,
    computeModeId: number,
    dateFormat: string,
    advanceComputeModeId: number,
    numberFormat: number,
    sortType?: string,
  ) => void;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  isDrag: boolean;
  setIsDrag: (flag: boolean) => void;
  deleteItem: (index: number) => void;
  // 右侧是否拖拽起来
  isDraggingField: boolean;
}

export const Box = ({
  col,
  index,
  onClick,
  getRootItems,
  more,
  moveCard,
  allowedDropEffect,
  isDrag,
  setIsDrag,
  deleteItem,
  isDraggingField,
  extraParams,
}: BoxProps) => {
  const AggregateText =
    col?.computeModeId && !col.isAggr
      ? `(${Aggregate[col?.computeModeId as AggregateType]})`
      : '';
  const uniqKey = `${col.columnId}-${col?.id}`;

  const sortIcon = sortIconMap[col?.sortType ?? ''] ?? '';
  const { tooltipEnable, textRef } = useTooltipShow(
    AggregateText + col.title,
    col?.sortType,
    more,
  );
  const ColumnDOM = (
    <div className={styles['column']} key={uniqKey}>
      {more ? <DragIcon className={'operation-drag'} /> : null}
      {sortIcon}
      <span className={styles.text} ref={textRef}>
        {AggregateText}
        {col.title}
      </span>
      <Dropdown
        menu={{
          items: getRootItems(
            col.fieldType,
            col.dataType,
            index,
            col.isAggr,
            col.summationComputeModeId,
            col.computeModeId,
            col.dateFormat + '',
            col.advanceComputeModeId,
            col.numberFormat,
            col.sortType,
          ),
          onClick: onClick(index),
        }}
      >
        <a
          onClick={(e) => e.preventDefault()}
          className={styles['operation-menu']}
        >
          <div id={'__OperationId'} className={styles['new-guide-wrap']}></div>
          <MoreOutlinedIcon className={styles['operation']} />
        </a>
      </Dropdown>
    </div>
  );
  const TooltipDOM = tooltipEnable ? (
    <Tooltip title={isDrag ? '' : col.title} mouseLeaveDelay={0}>
      {ColumnDOM}
    </Tooltip>
  ) : (
    ColumnDOM
  );

  const setDragFlag = useCallback(
    (isDragging: boolean) => {
      setIsDrag(isDragging);
    },
    [setIsDrag],
  );

  return (
    <Card
      key={uniqKey}
      index={index}
      id={uniqKey}
      moveCard={moveCard}
      dragType={ItemTypes.CARD + allowedDropEffect}
      allowedDropEffect={allowedDropEffect}
      setDragFlag={setDragFlag}
      deleteItem={deleteItem}
      isDraggingField={isDraggingField}
      extraParams={extraParams}
    >
      {TooltipDOM}
    </Card>
  );
};
