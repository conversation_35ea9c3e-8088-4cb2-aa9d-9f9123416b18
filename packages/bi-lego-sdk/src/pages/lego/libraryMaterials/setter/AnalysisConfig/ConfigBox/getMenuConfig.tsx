// @ts-nocheck
// 获取菜单配置项
import { cloneDeep } from 'lodash-es';
import { Dropdown } from '@blmcp/ui';
import { ReactComponent as DeleIcon } from '@/assets/lego/delete-menu.svg';
import { ReactComponent as AddSumIcon } from '@/assets/lego/add-sum.svg';
import { ReactComponent as CancelSumIcon } from '@/assets/lego/cancel-sum.svg';
import { ReactComponent as QuickCalcIcon } from '@/assets/lego/quick-calc.svg';
import { ReactComponent as SumStyleIcon } from '@/assets/lego/sum-style.svg';
import { ReactComponent as RightRowIcon } from '@/assets/lego/right-arrow.svg';
import { ReactComponent as FormatIcon } from '@/assets/lego/format.svg';
import { ReactComponent as SortMenuIcon } from '@/assets/lego/sort-menu.svg';
import { CalcType, FieldType } from '../types';
import { dateItems, sortItems } from '../constant';
import styles from './index.module.less';

export interface ModifyBoxFun {
  (index: number, key: string, value?: string): void;
}

export interface SortClickFun {
  (index: number, value: string): void;
}

export const getMenuConfig =
  (
    combineModeIndex: Record<string, CalcType[]>,
    combineModeDim: Record<string, CalcType[]>,
    rateConfig: any,
    dateFormatDisabled = false,
    numberFormatConfig = false,
    numberFormatIndexOnly = false,
    disabledSortConfig = false,

    modifyBox: ModifyBoxFun,
    onSortClick: SortClickFun,
  ) =>
  (
    fieldType: FieldType,
    dataType: number,
    index: number,
    isAggr: boolean,
    summationComputeModeId: number,
    computeModeId: number,
    dateFormat: string,
    advanceComputeModeId: number,
    numberFormat: number,
    sortType?: string,
  ) => {
    // 点击日期选项
    const onDateClick = (index: number) => (item: { key: string }) => {
      modifyBox(index, 'dateFormat', item.key);
    };

    const onItemSortClick = (index: number) => (item: { key: string }) => {
      onSortClick(index, item.key);
    };
    // 点击具体聚合方式
    const onAggregateClick = (index: number) => (item: { key: string }) => {
      modifyBox(index, 'computeModeId', item.key);
    };
    const onQuickClick = (index: number) => (item: { key: string }) => {
      if (!item.key) {
        modifyBox(index, 'advanceComputeModeId', '');
        modifyBox(index, 'numberFormat', undefined);
        return;
      }
      // 拆分同环比，百分率
      const splitArr = item.key.split('-');
      if (splitArr.length > 1) {
        modifyBox(index, 'advanceComputeModeId', splitArr[0]);
        modifyBox(index, 'numberFormat', splitArr[1]);
      } else {
        modifyBox(index, 'advanceComputeModeId', item.key);
        modifyBox(index, 'numberFormat', '2');
      }
    };

    const getQuickValue = (
      advanceComputeModeId: number,
      numberFormat: number,
    ) => {
      if (numberFormat) {
        return `${advanceComputeModeId}-${numberFormat}`;
      }
      return advanceComputeModeId;
    };

    const menu: any = [];
    // 如果是指标则拿指标配置
    if (fieldType === FieldType.Index) {
      // 如果是指标字段
      Object.keys(combineModeIndex)?.forEach((key) => {
        if (key === 'aggregate' && !isAggr) {
          menu.push({
            key: 'aggregate',
            label: (
              <Dropdown
                overlayStyle={{ zIndex: 100 }}
                autoAdjustOverflow={false}
                placement="bottomRight"
                menu={{
                  style: {
                    left: '-135px',
                    top: '-38px',
                    width: '150px',
                    position: 'relative',
                  },
                  items: combineModeIndex.aggregate,
                  selectable: true,
                  selectedKeys: [computeModeId],
                  defaultSelectedKeys: [computeModeId],
                  onClick: onAggregateClick(index),
                  subMenuCloseDelay: 0,
                }}
              >
                <div className={styles['operation-text']}>
                  <SumStyleIcon className={styles['operation-icon']} /> 聚合方式
                  <RightRowIcon className={styles.right} />
                </div>
              </Dropdown>
            ),
          });
        } else if (key === 'quick') {
          // 需要对同环比做特殊处理，因为同环比依赖，维度选择了日期，并且不同的日期选择维度展示不同的同环比选项

          const quickConfig = cloneDeep(combineModeIndex.quick);
          const idx = quickConfig?.findIndex((quick) => quick.key === 'rate');
          const percent = quickConfig?.find((quick) => quick.key === 'percent');

          if (percent) {
            if (percent.column) {
              if (percent.children) {
                percent.popupOffset = [-295, -5];
                percent.children.push({
                  key: '30-2',
                  label: (
                    <span
                      style={{
                        width: '120px',
                        display: 'inline-block',
                        lineHeight: '24px',
                      }}
                    >
                      按列
                    </span>
                  ),
                });
              } else {
                percent.popupOffset = [-295, -5];
                percent.children = [
                  {
                    key: '30-2',
                    label: (
                      <span
                        style={{
                          width: '120px',
                          display: 'inline-block',
                          lineHeight: '24px',
                        }}
                      >
                        按列
                      </span>
                    ),
                  },
                ];
              }
            }
          }
          if (idx >= 0) {
            if (rateConfig.length > 0) {
              quickConfig[idx].popupOffset = [-295, -5];
              quickConfig[idx].children = rateConfig;
            } else {
              quickConfig.splice(idx, 1);
            }
          }

          if (quickConfig.length > 0) {
            // 无操作
            quickConfig.push({
              key: '',
              label: (
                <span
                  style={{
                    width: '120px',
                    display: 'inline-block',
                    lineHeight: '24px',
                  }}
                >
                  无
                </span>
              ),
            });
            menu.push({
              key: 'quick',

              label: (
                <Dropdown
                  disabled={summationComputeModeId}
                  overlayStyle={{ zIndex: 100 }}
                  menu={{
                    selectedKeys: [
                      getQuickValue(advanceComputeModeId, numberFormat),
                    ],
                    defaultSelectedKeys: [
                      getQuickValue(advanceComputeModeId, numberFormat),
                    ],
                    style: {
                      left: '-135px',
                      top: '-38px',
                      width: '150px',
                      position: 'relative',
                    },
                    items: quickConfig,
                    selectable: true,

                    onClick: onQuickClick(index),
                  }}
                  placement="bottomRight"
                >
                  <div
                    className={
                      styles[
                        !summationComputeModeId
                          ? 'operation-text'
                          : 'operation-text-disabled'
                      ]
                    }
                  >
                    <QuickCalcIcon className={styles['operation-icon']} />{' '}
                    快速计算 <RightRowIcon className={styles.right} />{' '}
                  </div>
                </Dropdown>
              ),
            });
          }
        } else if (key === 'total') {
          const totalFlag = `${summationComputeModeId}` === '31';
          menu.push({
            disabled: advanceComputeModeId,
            key: totalFlag ? 'total-cancel' : 'total',
            label: (
              <div
                className={
                  styles[
                    !advanceComputeModeId
                      ? 'operation-text'
                      : 'operation-text-disabled'
                  ]
                }
              >
                {totalFlag ? (
                  <CancelSumIcon className={styles['operation-icon']} />
                ) : (
                  <AddSumIcon className={styles['operation-icon']} />
                )}
                {totalFlag ? '取消总计' : '添加总计'}
              </div>
            ),
          });
        }
      });
      //如果是维度，则拿维度配置
    } else if (fieldType === FieldType.Dim) {
      // 如果是维度字段
      Object.keys(combineModeDim)?.forEach((key) => {
        if (key === 'aggregate') {
          menu.push({
            key: 'aggregate',
            label: (
              <Dropdown
                overlayStyle={{ zIndex: 100 }}
                menu={{
                  style: {
                    left: '-135px',
                    top: '-38px',
                    width: '150px',
                    position: 'relative',
                  },
                  items: combineModeDim.aggregate,
                  selectedKeys: [computeModeId],
                  defaultSelectedKeys: [computeModeId],
                  selectable: true,
                  onClick: onAggregateClick(index),
                }}
                placement="bottomRight"
              >
                <div className={styles['operation-text']}>
                  <SumStyleIcon className={styles['operation-icon']} /> 聚合方式
                  <RightRowIcon className={styles.right} />
                </div>
              </Dropdown>
            ),
          });
        }
        // 如果维度类型为日期，则展示日期类型
      });
      if (dataType === 2 && !dateFormatDisabled) {
        menu.push({
          key: 'dateType',
          label: (
            <Dropdown
              overlayStyle={{ zIndex: 100 }}
              menu={{
                style: {
                  left: '-135px',
                  top: '-38px',
                  width: '150px',
                  position: 'relative',
                },
                items: dateItems,
                selectable: true,
                selectedKeys: [dateFormat],
                defaultSelectedKeys: [dateFormat],
                onClick: onDateClick(index),
              }}
              placement="bottomRight"
            >
              <div className={styles['operation-text']}>
                <QuickCalcIcon className={styles['operation-icon']} /> 日期类型
                <RightRowIcon className={styles.right} />
              </div>
            </Dropdown>
          ),
        });
      }
    }

    // numberFormatIndexOnly
    if (numberFormatConfig) {
      if (numberFormatIndexOnly) {
        if (fieldType === FieldType.Index) {
          menu.push({
            key: 'format',
            label: (
              <div className={styles['operation-text']}>
                <FormatIcon className={styles['operation-icon']} /> 数据格式
              </div>
            ),
          });
        }
      } else {
        menu.push({
          key: 'format',
          label: (
            <div className={styles['operation-text']}>
              <FormatIcon className={styles['operation-icon']} /> 数据格式
            </div>
          ),
        });
      }
    }
    // 排序设置
    if (!disabledSortConfig) {
      menu.push({
        key: 'sort',
        label: (
          <Dropdown
            overlayStyle={{ zIndex: 100 }}
            menu={{
              style: {
                left: '-135px',
                top: '-38px',
                width: '150px',
                position: 'relative',
              },
              items: sortItems,
              selectable: true,
              selectedKeys: [sortType],
              defaultSelectedKeys: [sortType],
              onClick: onItemSortClick(index),
            }}
            placement="bottomRight"
          >
            <div className={styles['operation-text']}>
              <SortMenuIcon className={styles['operation-icon']} /> 排序
              <RightRowIcon className={styles.right} />
            </div>
          </Dropdown>
        ),
      });
    }
    // 遍历依次加入
    menu.push({
      key: 'delete',
      label: (
        <div className={styles['operation-text']}>
          <DeleIcon className={styles['operation-icon']} /> 删除
        </div>
      ),
    });
    return menu;
  };
