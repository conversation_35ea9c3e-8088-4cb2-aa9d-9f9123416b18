// @ts-nocheck
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Input, Select, Skeleton, Collapse, Empty, message } from '@blmcp/ui';
import { CaretRightOutlined, SearchOutlined } from '@ant-design/icons';

import { useRequest } from 'ahooks';

import { debounce } from 'lodash-es';
import { ReactComponent as CollapseIconIcon } from '@/assets/lego/collapse-icon.svg';
import { ReactComponent as FoldIcon } from '@/assets/lego/fold.svg';

import { DataSourceItem } from '@/pages/lego/api/types';
import { queryDataSourceDetail, queryDataSourceList } from '@/pages/lego/api';

import { getSelectComponentNode } from '@/pages/lego/utils';
import { store } from '@/pages/lego/hooks/useComponent';
import {
  useTrendComponentDataSourceOptions,
  useTrendComponentOptionRender,
  useIsTrendComponent,
} from './utils';
import { AddFun, FieldType, SetterData } from '../types';
import { Box } from '../Box';
import Calc from '../../../../modules/editPage/components/AddCalculateFields';
import { getPreviousDataSource } from '../../../module/utils';
import styles from '../index.module.less';
import { Text } from './Text';
import { filterSenstiveData } from './handleData';

const { Panel } = Collapse;

interface DataSourcePanelProps {
  add: AddFun;
  dataSourceId: number;
  dimDisabled: boolean;
  indexDisabled: boolean;
  // 只支持日期格式的数据
  dateOnly: boolean;
  setting: SetterData;
  draggingRef: unknown;
  dateDisabled?: boolean;
  dataSetDisabled?: boolean;
  setDataSourceId: (id: number) => void;
}

export const DataSourcePanel = ({
  add,
  dataSourceId: currentDataSourceId,
  dimDisabled,
  indexDisabled,
  dateOnly,
  dateDisabled,
  setting,
  draggingRef,
  dataSetDisabled,
  setDataSourceId: setDataSourceIdFromParent,
}: DataSourcePanelProps) => {
  const [dataSourceId, setDataSourceId] = useState(currentDataSourceId);
  const [searchText, setSearchText] = useState('');
  const [relSearchText, setRealText] = useState('');
  const [dataSourceList, setDataSourceList] = useState<DataSourceItem[]>([]);
  const compDlRef = useRef();
  const selectRef = useRef<HTMLSelectElement>();
  const [updateId, setUpdateId] = useState(0);
  const [activeKey, setActiveKey] = useState<string[]>([]);
  // 搜索的时候 展开active
  const [activeKeySearch, setActiveKeySearch] = useState<string[]>([]);
  // 锚点
  const anchorDim = useRef<HTMLDivElement>();
  const anchorIndex = useRef<HTMLDivElement>();

  // 判断当前是否为趋势组件
  const isTrendComponent = useIsTrendComponent();

  // 数据集数据源添加置灰属性
  const dataSourceOptions = useTrendComponentDataSourceOptions(dataSourceList);

  // 实时数据集支持tooltip提示
  const optionRender = useTrendComponentOptionRender();

  // 获取可用的数据源（过滤掉会被置灰的数据源）
  const getAvailableDataSources = (dataSourceList: DataSourceItem[]) => {
    if (!isTrendComponent) {
      return dataSourceList;
    }
    return dataSourceList.filter(
      (item) => ![1, 2, 3].includes(item?.dataUpdateType),
    );
  };

  const { data, run, loading, runAsync } = useRequest(queryDataSourceDetail, {
    manual: true,
  });
  useEffect(() => {
    if (dataSourceId && !dataSetDisabled) {
      // 数据集列表需有当前数据集id 才可查询
      if (
        !currentDataSourceId ||
        dataSourceList.some((s) => s.id === dataSourceId)
      ) {
        run({ dataSourceId });
      } else if (
        currentDataSourceId &&
        dataSourceList.length &&
        !dataSourceList.some((s) => s.id === dataSourceId)
      ) {
        message.error('当前用户未拥有该数据集权限!');
      }
    }
  }, [dataSourceId, dataSourceList, run]);

  const getDataSource2JumptoNew = async (
    dataSourceId: number,
    type: 'dim' | 'index' | '',
  ) => {
    await runAsync({ dataSourceId });

    if (type === 'dim') {
      const option = {};

      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      const io = new IntersectionObserver(callback, option);
      io.observe(anchorDim?.current);

      function callback(entries) {
        if (entries[0].isIntersecting) {
          setTimeout(() => {
            anchorDim?.current?.classList.add('anchor-emphasize');
            if (anchorDim?.current) {
              io.unobserve(anchorDim?.current);
            }
            // 关闭观察器
            io.disconnect();
            setTimeout(() => {
              anchorDim?.current?.classList.remove('anchor-emphasize');
            }, 500);
          }, 300);
        }
      }
      if (anchorDim) {
        anchorDim?.current?.scrollIntoView?.({
          block: 'center',
          behavior: 'smooth',
        });
      }
    } else if (type === 'index') {
      const option = {};

      const io = new IntersectionObserver(callback, option);
      io.observe(anchorIndex?.current);

      function callback(entries) {
        if (entries[0].isIntersecting) {
          setTimeout(() => {
            anchorIndex?.current?.classList.add('anchor-emphasize');
            if (anchorIndex?.current) {
              io.unobserve(anchorIndex?.current);
            }
            // 关闭观察器
            io.disconnect();
            setTimeout(() => {
              anchorIndex?.current?.classList.remove('anchor-emphasize');
            }, 500);
          }, 300);
        }
      }
      if (anchorIndex) {
        anchorIndex?.current?.scrollIntoView?.({
          block: 'center',
          behavior: 'smooth',
        });
      }
    } else {
    }
  };

  const dimensionList = (data?.data?.dimensionList ?? []).filter((dim) =>
    dim.title.includes(relSearchText),
  );

  const measureNoGroupList = useMemo(
    () =>
      (data?.data?.measureList ?? [])
        .map((item) => {
          return {
            ...item,
            columnList: item.columnList.filter((it) =>
              it.title.includes(relSearchText),
            ),
          };
        })
        .filter((it) => it.columnList.length > 0),
    [data?.data?.measureList, relSearchText],
  );

  const measureList = measureNoGroupList;
  // 数据源切换
  const onChange = (value: number) => {
    setDataSourceId(value);
    // 清空搜索条件
    setSearchText('');
    setRealText('');
    run({ dataSourceId: value });
    // 失去焦点操作
    setTimeout(() => {
      selectRef?.current?.blur();
    }, 0);
  };

  // 初始化
  useEffect(() => {
    const getData = async () => {
      const res = await queryDataSourceList();
      const dataSourceList = res?.data ?? [];
      setDataSourceList(dataSourceList);

      // 获取可用的数据源（过滤掉会被置灰的数据源）
      const availableDataSources = getAvailableDataSources(dataSourceList);

      if (!dataSourceId) {
        // 没有设置数据源ID时的初始化逻辑
        const previousDataSource = getPreviousDataSource();

        // 如果之前选择的数据源在可用列表中，则使用它；否则使用第一个可用的数据源
        const targetDataSourceId =
          previousDataSource &&
          availableDataSources.some((ds) => ds.id === previousDataSource)
            ? previousDataSource
            : availableDataSources[0]?.id;

        if (targetDataSourceId) {
          setDataSourceId(targetDataSourceId);
        }
      } else {
        // 已有数据源ID时，检查是否在可用列表中
        const isCurrentDataSourceAvailable = availableDataSources.some(
          (ds) => ds.id === dataSourceId,
        );

        if (!isCurrentDataSourceAvailable && availableDataSources.length > 0) {
          // 当前数据源不可用（会被置灰），选择第一个可用的数据源
          const newDataSourceId = availableDataSources[0].id;
          setDataSourceId(newDataSourceId);
          // 同时更新上层的 setting
          setDataSourceIdFromParent(newDataSourceId);
        }
      }
    };

    getData();
  }, [run, setDataSourceId, isTrendComponent, setDataSourceIdFromParent]);
  const filterOption = useCallback(
    (input: string, option?: { label: string; value: string }) =>
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
    [],
  );

  const filterField = debounce(async (str: string) => {
    setRealText(str);

    const _measureList = (data?.data?.measureList ?? [])
      .map((item) => {
        return {
          ...item,
          columnList: item.columnList.filter((it) => it.title.includes(str)),
        };
      })
      .filter((it) => it.columnList.length > 0);
    const activeKeys =
      str !== ''
        ? _measureList?.map((itemGroup) => itemGroup.category) ?? []
        : [];
    setActiveKeySearch(activeKeys);
  }, 1000);
  // 搜索的时候
  const onSearch = (e: { target: { value: string } }) => {
    setSearchText(e.target.value);
    filterField(e.target.value);
  };

  useEffect(() => {
    if (relSearchText) {
      const activeKeys =
        measureList?.map((itemGroup) => itemGroup.category) ?? [];
      setActiveKeySearch(activeKeys);
    }
  }, [measureList, relSearchText]);
  const defaultActiveKey = [];
  // 如果维度或指标不可用，则默认收起（UI设计师@豆浩晴 要求）
  if (!dimDisabled) {
    defaultActiveKey.push('1');
  }
  if (!indexDisabled) {
    defaultActiveKey.push('2');
  }

  // 获取每一个指标分组的DOM

  const getItems = (dimGroup = []) => {
    return dimGroup?.map((itemGroup, index) => {
      return {
        key: itemGroup.category,
        className: styles['panel-sub-content'],
        label: (
          <span className={styles['fold-title-wrap']}>
            <FoldIcon className={styles['fold-icon']} />
            <Text text={itemGroup.category}>
              <span className={styles['fold-title']}>{itemGroup.category}</span>
            </Text>
          </span>
        ),
        children: itemGroup?.columnList?.map((dim, idx: number) => {
          let refProps = {};
          if (itemGroup.category === '自建指标') {
            if (updateId) {
              refProps =
                dim.columnId === updateId
                  ? {
                      innerRef: anchorIndex,
                    }
                  : {};
            } else {
              refProps =
                idx === itemGroup?.columnList?.length - 1
                  ? {
                      innerRef: anchorIndex,
                    }
                  : {};
            }
          }

          return (
            <Box
              setting={setting}
              {...refProps}
              disabled={indexDisabled}
              key={dim.columnId + dim.key}
              isAggr={dim.isAggr}
              fieldDesc={dim?.fieldDesc}
              name={dim.title}
              columnId={dim.columnId}
              add={add}
              field={dim.key}
              type={FieldType.Index}
              isEdit={`${dim.tenantFlag}` !== '0'}
              dataType={dim.dataType}
              dataSourceId={dataSourceId}
              update={(columnId) => {
                compDlRef.current?.update?.(columnId);
                setUpdateId(columnId);
              }}
              draggingRef={draggingRef}
              deleteCallback={() => {
                run({ dataSourceId });
              }}
            />
          );
        }),
      };
    });
  };

  const collapseOnChange = (value: string[]) => {
    if (searchText) {
      setActiveKeySearch(value);
    } else {
      setActiveKey(value);
    }
  };
  return (
    <div>
      <Select
        ref={selectRef}
        showSearch
        placeholder="请选择数据集"
        optionFilterProp="children"
        value={
          dataSourceList?.length > 0 &&
          !dataSetDisabled &&
          dataSourceList.some((s) => s.id === dataSourceId)
            ? dataSourceId
            : undefined
        }
        style={{ marginRight: 8, width: '180px' }}
        onChange={onChange}
        filterOption={filterOption}
        disabled={dataSetDisabled}
        options={dataSourceOptions}
        optionRender={optionRender}
      />

      <div className={styles['search-panel']} id="__Search_DataSource">
        <Input
          value={searchText}
          placeholder="搜索字段名称"
          onChange={onSearch}
          allowClear
          prefix={<SearchOutlined style={{ color: 'rgba(0, 0, 0, 0.3)' }} />}
        />
      </div>
      <div className={styles['new-field-wrap']}>
        {dataSetDisabled ? (
          <span className={styles['new-field-disabled']}>新建计算字段</span>
        ) : (
          <Calc
            ref={compDlRef}
            dataSourceId={dataSourceId}
            wdData={(data?.data?.dimensionList ?? []).filter(
              (item) => item.isSensitive !== 1,
            )}
            _dlData={filterSenstiveData(data?.data?.measureList ?? [])}
            onOk={({ dataType, isNew, columnId }) => {
              if (isNew) {
                setUpdateId(0);
              }
              setActiveKey([...activeKey, '自建指标']);
              getDataSource2JumptoNew(
                dataSourceId,
                dataType === 1 ? 'index' : 'dim',
              );

              // 找到当前光标的图表，查询是否使用了当前字段，如使用则重新查下数据
              const seleceNode = getSelectComponentNode();
              if (seleceNode && !isNew) {
                const dataSetConfig: any =
                  seleceNode.schema?.props?.dataSetConfig || {};
                const isHasColumnId = Object.keys(dataSetConfig).map((key) => {
                  if (typeof dataSetConfig[key] !== 'object') {
                    return false;
                  } else {
                    return dataSetConfig[key].some(
                      (f) => f.columnId === columnId,
                    );
                  }
                });

                if (
                  dataSetConfig.dataSourceId === dataSourceId &&
                  isHasColumnId.some((v) => v)
                )
                  store.get(seleceNode._id)?.query?.({ cache: false });
              }
            }}
          >
            <span className={styles['new-field']}>新建计算字段</span>
          </Calc>
        )}
      </div>
      <div className={styles['data-source-detail']}>
        {loading ? (
          <Skeleton />
        ) : (
          <Collapse
            bordered={false}
            defaultActiveKey={defaultActiveKey}
            expandIconPosition="end"
            expandIcon={({ isActive }: { isActive: boolean }) => (
              <CollapseIconIcon
                style={{ transform: `rotate(${isActive ? '0deg' : '-90deg'})` }}
              />
            )}
            className={styles['collapse']}
          >
            <Panel
              disabled={dimDisabled}
              header={
                <div className={styles['collapse-header']}>
                  <span className={styles['panel-title']}>
                    <span>维度</span>
                  </span>
                </div>
              }
              key="1"
              className={styles['panel-content']}
            >
              <p className={styles.content}>
                {dimensionList.length === 0 ? (
                  <div className={styles.empty}>
                    <Empty
                      description={searchText ? '找不到该维度' : '暂无数据'}
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  </div>
                ) : (
                  dimensionList.map((dim, index) => {
                    let refProps = {};
                    if (updateId) {
                      refProps =
                        dim.columnId === updateId
                          ? {
                              innerRef: anchorDim,
                            }
                          : {};
                    } else {
                      refProps =
                        index === dimensionList.length - 1
                          ? {
                              innerRef: anchorDim,
                            }
                          : {};
                    }

                    return (
                      <Box
                        setting={setting}
                        {...refProps}
                        disabled={
                          dimDisabled ||
                          (dateOnly && dim.dataType !== 2) ||
                          (dateDisabled && dim.dataType === 2)
                        }
                        key={dim.columnId + dim.key}
                        columnId={dim.columnId}
                        isAggr={dim.isAggr}
                        name={dim.title}
                        fieldDesc={dim?.fieldDesc}
                        add={add}
                        field={dim.key}
                        dataType={dim.dataType}
                        type={FieldType.Dim}
                        isEdit={`${dim.tenantFlag}` !== '0'}
                        dataSourceId={dataSourceId}
                        draggingRef={draggingRef}
                        update={(columnId) => {
                          compDlRef.current?.update?.(columnId);
                          setUpdateId(columnId);
                        }}
                        deleteCallback={() => {
                          run({ dataSourceId });
                        }}
                      />
                    );
                  })
                )}
              </p>
            </Panel>
            <Panel
              disabled={indexDisabled}
              header={
                <div className={styles['collapse-header']}>
                  <span className={styles['panel-title']}>指标</span>
                </div>
              }
              key="2"
              className={styles['panel-content']}
            >
              <p className={styles.content}>
                {measureList?.length === 0 ? (
                  <div className={styles.empty}>
                    <Empty
                      description={searchText ? '找不到该指标' : '暂无数据'}
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  </div>
                ) : (
                  <Collapse
                    key={'index'}
                    bordered={false}
                    activeKey={searchText ? activeKeySearch : activeKey}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined rotate={isActive ? 90 : 0} />
                    )}
                    items={getItems(measureList)}
                    onChange={collapseOnChange}
                  />
                )}
              </p>
            </Panel>
          </Collapse>
        )}
      </div>
    </div>
  );
};
