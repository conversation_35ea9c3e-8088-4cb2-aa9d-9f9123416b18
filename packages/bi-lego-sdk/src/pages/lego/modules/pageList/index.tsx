import React, { useState, useEffect, Component } from 'react';
import { Tabs } from '@blmcp/ui';
// import { useSearchParams } from 'react-router-dom';
import getAssets from '@/pages/lego/modules/editPage/services/getAssets';
import { loadCDN } from '@/utils/loadCompontent';
import CommonTable from './components/CommonTable';
import { Template } from './components/Tempate';
import { Header } from './components/Header';

import HubblePage from './components/HubblePage';
// @ts-expect-error
import styles from './index.module.less';
import config from '@/tool/config';
import { isLegoDev } from '@/utils/common';
// @ts-expect-error
import { ReactComponent as WelcomeIcon } from '@/assets/lego/welcome.svg';
// @ts-expect-error
import welcomeStyles from './components/Header/index.module.less';

const PageList = (props) => {
  const searchParams = new URLSearchParams(location.search.slice(1));
  const listPermission = config.get('sdkConfig.listPermission');
  const browsingHistory = config.get('sdkConfig.browsingHistory');

  const onChange = (key: string) => {
    setTabKey(key);
  };
  // 判断是否有报告详情页面权限，区分制表人和看表人
  const pageListPermission = props.showEditTab;

  const PublicTemplate = {
    key: '3',
    label: '公共模板',
    children: <Template pageListPermission={pageListPermission} />,
  };

  const ICreated = {
    key: '1',
    label: '我创建的',
    children: (
      <CommonTable tabKey="1" pageListPermission={pageListPermission} />
    ),
  };

  const Shared = {
    key: '2',
    label: '被分享的',
    children: (
      <CommonTable tabKey="2" pageListPermission={pageListPermission} />
    ),
  };

  const templateMap = [
    {
      key: 'public',
      Component: PublicTemplate,
    },
    {
      key: 'mine',
      Component: ICreated,
    },
    {
      key: 'shared',
      Component: Shared,
    },
  ];

  let items: any = [];
  if (isLegoDev()) {
    // 哈勃场景
    items = [
      {
        key: '4',
        label: '公共模板',
        children: <HubblePage tabKey={4} brandList={props?.brandList ?? []} />,
      },
      {
        key: '5',
        label: '我创建的',
        children: <HubblePage tabKey={1} brandList={props?.brandList ?? []} />,
      },
    ];
  } else if (pageListPermission) {
    // SP 有编辑页权限
    items = templateMap
      .filter(
        (v) =>
          ['public', 'mine', 'shared'].includes(v.key) &&
          listPermission.includes(v.key),
      )
      .map((v) => v.Component);
  } else {
    // SP 无编辑页权限
    items = templateMap
      .filter(
        (v) =>
          ['public', 'shared'].includes(v.key) &&
          listPermission.includes(v.key),
      )
      .map((v) => v.Component);
  }

  const [tabKey, setTabKey] = useState<string>(
    searchParams.get('listKey') ?? (isLegoDev() ? '4' : items[0].key),
  );

  useEffect(() => {
    setTimeout(() => {
      window.$legoAssets = window.$legoAssets || {};
      const assets = getAssets();
      assets.packages.forEach((item) => {
        item.urls.forEach((url) => {
          if (!window.$legoAssets || window.$legoAssets[item.library]) return;
          window.$legoAssets[item.library] = true;
          loadCDN(url);
        });
      });
    }, 1000);
  }, []);

  return (
    <div className={styles['legoList']}>
      {!isLegoDev() ? (
        <div className={welcomeStyles['welcome']}>
          <div>
            <div className={welcomeStyles['welcome-title']}>
              👏欢迎来到数据乐高！
            </div>
            <div className={welcomeStyles['welcome-subtitle']}>
              数据乐高提供通用分析模板，支持快速搭建数据报告，从而提升看数和分析效率，沉淀分析方法，助力提升经营效率。
            </div>
          </div>
          <WelcomeIcon className={welcomeStyles['welcome-icon']} />
        </div>
      ) : null}
      {props.beforeSlot}
      {!isLegoDev() && browsingHistory ? <Header /> : null}
      {props.afterSlot}
      <div className={styles['legoList-content']}>
        <Tabs
          activeKey={tabKey}
          items={items}
          onChange={(e) => onChange(e)}
          destroyInactiveTabPane={true}
        />
      </div>
    </div>
  );
};

export default PageList;
