.lowcode-plugin-sample-box {
  overflow: auto !important;
  padding: 0;
  margin: 0;
  position: relative;
  flex: 1;

  .lowcode-plugin-sample-preview-content {
    height: auto !important;
    padding: 0px 10px 0px !important;
    .legoFilterDefaultRow{
      top: 0px;
      width: calc(100% + 20px);
      transform: translateX(-10px);
      border-radius: 0;
      &::after{
        border-radius: 0;
      }
    }
  }
}

.mobile-container {
  touch-action: manipulation;
}

.lowcode-plugin-sample-preview {
  height: 100%;
  font-family:
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Helvetica,
    Arial,
    sans-serif;
  font-family: var(--font-family);
  font-size: 12px;
  font-size: var(--font-size-text);
  color: rgba(0, 0, 0, 0.6);
  color: var(--color-text);
  background-color: #edeff3;
  display: flex;
  flex-direction: column;
}

.lowcode-plugin-sample-header {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0 10px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  justify-content: space-between;
  >div{
    display: flex;
  }

  > h1 {
    padding: 0;
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.9);
  }
}

.lego-page-loading {
  width: 100%;
  height: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

// 哈勃页面
.lowcode-plugin-sample-preview.isHubble {
  .lowcode-plugin-sample-preview-content {
    height: auto !important;
    padding: 10px 0px 0px !important;
  }
  .legoFilterDefaultRow{
    padding: 10px 20px !important;
  }
}

#_overScrollWrap.hubble-qbi-height100 > div {
  height: 100%;
  > .ice-stark-loaded {
    height: 100%;
    > #root {
      height: 100%;
    }
  }
}

// 移动页面
.lowcode-plugin-sample-preview {
  &.mobile {
    .lowcode-plugin-sample-header {
      position: relative;
      z-index: 10;
      >h1{
        font-size: 18px;
        max-width: inherit;
      }
    }
    .lowcode-plugin-sample-box {
      .lowcode-plugin-sample-preview-content {
        padding-left: 0 !important;
        padding-top: 0 !important;
        padding-right: 0 !important;
        padding-bottom: 100px !important;
      }
    }
  }
}
.blm-cascader-popup-body-inner {
  height: 90svh !important;
  .blm-cascader-tree {
    height: calc(90svh - 56px - 48px - 55px) !important;
  }
}

// 移动版筛选器适配
.blm-popup-body {
  .blm-cascader-search-icon {
    line-height: 22px;
    height: 22px;
    font-size: 16px;
  }
  .blm-cascader-tree {
    .blm-tree-select-multiple-item {
      .blm-checkbox {
        --icon-size: 18px;
        --blm-interactive-size: 11px;
      }
    }
  }
  .blm-cascader-tools {
    .blm-button {
      --blm-button-border-radius: 8px;
    }
  }
}
