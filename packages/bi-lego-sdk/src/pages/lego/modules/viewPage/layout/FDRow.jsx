import React, { forwardRef } from 'react';
import { wrapReactClass } from '@alilc/lowcode-utils';

export const FDRow = forwardRef(({ children, className }, ref) => {
  const isLegoDefaultRow = className && className?.indexOf('legoDefaultRow') !== -1;

  const filterChildren = children.filter((f) => f.props?.style?.display !== 'none');
  const childrenLength  = filterChildren.length;
  let content = filterChildren
  if (isLegoDefaultRow) {
    content = (
      <div className={`lego-default-row-wrapper ${childrenLength > 4 ? 'show-expand' : 'hide-expand'}`}>
        <div className='lego-default-row-filter-wrapper'>{filterChildren.filter((item, index) => index !== filterChildren?.length - 1)}</div>
        {filterChildren?.[filterChildren?.length - 1]}
      </div>
    )
  }
  return (
    <div className={`${className || ''} m-fd-row mobile`} ref={ref}>
      {content}
    </div>
  )
})

export default wrapReactClass(FDRow)
