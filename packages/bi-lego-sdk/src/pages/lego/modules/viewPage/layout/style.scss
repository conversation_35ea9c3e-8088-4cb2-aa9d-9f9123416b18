.lowcode-plugin-sample-preview {
  .lce-page {
    .legoFilterDefaultRow {
      &.mobile {
        background-color: white;
        padding: 0 0 10px 0;
        width: 100%;
        transform: none;
        .hide-expand {
          .condition-expand-icon {
            display: none;
          }
        }
        .lego-default-row-wrapper {
          background-color: white;
          padding: 10px;
          box-sizing: border-box;
          position: relative;
          .m-fd-cell {
            margin: 0 0 0 0;
          }
          .lego-default-row-filter-wrapper {
            height: 35px;
            overflow: hidden;
            display: grid;
            grid-template-columns: 33.3% 33.3% 33.3%;
            .m-fd-cell {
              font-size: 14px;
              margin: 0 0 10px 0;
              display: inline-block;
              vertical-align: top;
              padding-right: 3px;
              .lego-filter-wrap {
                height: 30px;
                line-height: 30px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 5px;
                display: block;
              }
              .blm-select{
                width: 100%;
                .blm-select-selector-label-wrapper{
                  max-width: calc(100% - 20px);
                }
                .blm-select-selector-label{
                  font-size: 16px;
                  display: inline-block;
                  height: 30px;
                  line-height: 30px;
                  color: #000;
                }
                .blm-select-selector>svg{
                  fill: #000
                }
              }
              .blm-cascader {
                .blm-cascader-feedback {
                  >*{
                    vertical-align: middle;
                  }
                  svg {
                    margin-left: 6px;
                    top: 0px !important;
                  }
                  span {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: inline-block;
                    height: 30px;
                    line-height: 30px;
                    margin: 0;
                    max-width: calc(100% - 20px);
                    font-size: 16px;
                  }
                }
              }
              .lego-date-picker-filter {
                .blm-web-ui-date-range-picker {
                  .blm-web-ui-date-range-picker-placeholder {
                    >*{
                      vertical-align: middle;
                    }
                    svg {
                      top: 0px;
                      margin-left: 6px;
                    }
                    span {
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: inline-block;
                      height: 30px;
                      line-height: 30px;
                      margin: 0;
                      max-width: calc(100% - 20px);
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }
          &.expand {
            .lego-default-row-filter-wrapper {
              height: auto;
              overflow: visible;
            }
            .condition-expand-icon {
              svg {
                transform: rotateZ(180deg);
              }
            }
          }
        }
        .condition-expand-icon {
          margin-top: 8px;
          text-align: center;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          svg {
            position: relative;
            top: -2px;
          }
        }
        display: block;
        &:after {
          content: none;
        }
        &:before {
          content: none;
        }
        .ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover{
          border-color: #E7E8EB;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
  }
}

.lowcode-plugin-sample-preview.mobile {
  .m-fd-cell {
    &.fdCell-XTab {
      min-height: auto;
    }
    .m-fd-row {
      padding: 0;
    }
  }
  .m-fd-row {
    padding: 0 10px;
    &.legoFilterDefaultRow {
      background-color: #edeff3;
    }
    .m-fd-cell {
      margin-bottom: 10px;
    }
  }
}
