// @ts-nocheck
import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { useEffect, useState } from 'react';
import { debounce } from 'lodash-es';
import { message } from '@blmcp/ui';
// @ts-expect-error
import { ReactComponent as ExportIconWhite } from '@/assets/lego/export.svg';
import { getCurrentPageId, walkerComponents } from '@/pages/lego/utils';
import { LegoExport } from '@/pages/lego/components/LegoExport';
import { isLegoDev } from '@/utils/common';
import useComponent from '@/pages/lego/hooks/useComponent';
import { globalCache } from '@/pages/lego/utils/cache';
import { store as reportStore } from '@/tool/report';
import sdkEvent from '@/tool/event';
// @ts-expect-error
import styles from './index.module.less';
import { filterComponents } from '@/pages/lego/config';

interface FilterType {
  bizColumn: string;
  value: string[];
  displayValue: string;
  overflowTooltip: boolean;
}

function ExportIcon(props) {
  const [searchParams, setSearchParams] = useState({});
  const [otherParams, setOtherParams] = useState({});
  const { config } = window.AliLowCodeEngine;
  const reportId = config.get('reportId');
  let chartId = null;
  walkerComponents(props.node.schema, (item) => {
    chartId = item.id;
  });
  const [meta] = useComponent(chartId);
  useEffect(() => {
    const exportBtn = document.querySelector('.export-container');
    if (exportBtn) {
      exportBtn.style = 'width: 1px; height: 0; overflow: hidden;';
    }
  }, [props.node.schema]);
  const hidden = ['XTab', 'Text', 'IndexCard', 'Card', ...filterComponents];
  if (
    // !(reportStore.get(reportId) ? reportStore.get(reportId).canExport : true) ||
    !meta ||
    !meta.isRender ||
    props?.meta === undefined ||
    hidden.includes(props.meta.componentName) ||
    isLegoDev()
  ) {
    return null;
  }
  const exportData = debounce(() => {
    sdkEvent.dispatch('templateExport', {
      reportId: getCurrentPageId(),
    });
    walkerComponents(props.node.schema, (item) => {
      try {
        if (Object.keys(globalCache?.relationshipMap).length > 0) {
          const oldValue = globalCache?.relationshipMap?.[item.id]?.[2];
          const query = JSON.parse(oldValue);
          const filterInfo = query?.filterInfo ?? [];
          const filters: FilterType[] = [];
          filterInfo.forEach((filter) => {
            filters.push({
              bizColumn: filter.key,
              value: filter.fieldValue,
              displayValue: filter.fieldLabel?.join('、'),
              overflowTooltip: true,
            });
          });

          setSearchParams(filters);
          setOtherParams(query);
          document.querySelector('.export-container button')?.click();
        } else {
          message.error('数据未加载完成，请稍后重试');
        }
      } catch (e: any) {
        console.warn(e);
      }
    });
  }, 300);

  return (
    <div className={styles['icon']}>
      <ExportIconWhite
        onClick={() => {
          exportData();
        }}
      />
      <LegoExport
        searchParams={searchParams}
        otherParams={otherParams}
        uuid={getCurrentPageId()}
      />
    </div>
  );
}

// 保存功能示例
const DiyComponentIconPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { config } = ctx;
      const diyActions = config.get('diyActions') ?? [];
      diyActions.push(ExportIcon);
      config.set('diyActions', diyActions);
    },
  };
};
DiyComponentIconPlugin.pluginName = 'DiyComponentIconPlugin';
export default DiyComponentIconPlugin;
