import React, { useState, useEffect } from 'react';
import { Card } from '@blmcp/ui';
import { ExportTypeEnum, ExportLimitMaxNum } from '../types';

interface IExportDescriptionProps {
  exportType: ExportTypeEnum;
  pageFlag: string;
}

const ExportDescription: React.FC<IExportDescriptionProps> = ({
  exportType,
  pageFlag,
}) => {
  return (
    <Card title="导出说明" bordered={false} style={{ marginBottom: '16px' }}>
      {
        // 乐高数据导出
        exportType === ExportTypeEnum.BILEGO ? (
          <div className="export-description">
            1、报告为公共模板且导出实时数据时，图表、交叉表支持导出1500条数据，明细表支持导出200万条数据；其他情况的图表、交叉表支持导出1500条数据，明细表支持导出5000条数据；
            <br />
            2、数据导出时，图表中【数据格式】【日期类型】的数据转化功能将不会随数据一并导出；
            <br />
            3、实时数据，每小时整点更新数据；T-1离线数据，每日0点更新前一天的数据，因不同业务数据更新时长不一致，建议每日7点之后导出前一天的数据；
            <br />
            4、操作导出后，可在页面右上角的“导出记录”中查看导出结果；
            <br />
            5、数据处理完成后生成的excel文件，将为您保存7天，在此期间您可以将文件下载到电脑本地；
          </div>
        ) : null
      }
    </Card>
  );
};

export default ExportDescription;
