import config from './config';

interface Option {
  key: string;
  request: any;
  env: 'dev' | 'use';
  configApi: string;
}

export default async function legoInit(option: Option) {
  if (!option || !option.key) {
    throw new Error('Initialization option is required with a valid key.');
  }
  if (!option || !option.request) {
    throw new Error('Initialization option is required with a valid request.');
  }
  if (!option || !option.env) {
    throw new Error('Initialization option is required with a valid env.');
  }
  if (!option || !option.configApi) {
    throw new Error('Initialization option is required with a valid env.');
  }
  config.set('setting', option);
  await config.getLegoConfig();
}
