import { moduleTools, defineConfig } from '@modern-js/module-tools';

export default defineConfig({
  plugins: [moduleTools()],
  buildConfig: {
    define: {
      'process.env.NODE_ENV': 'production',
      'process.env.buildDate': new Date().toLocaleString(),
      'process.env.PUBLIC_PATH':
        'https://webstatic.<%= mainDomain %>/$SUB_PATH',
    },
    // alias: {
    //   '@': 'src',
    // },
    // dts: {
    //   abortOnError: false,
    // },
  },
  buildPreset({ extendPreset }) {
    return extendPreset('npm-library', {
      // autoExternal: false,
      input: {
        index: 'src/index.ts',
        utils: 'src/tool/index.ts',
        LegoEditView: 'src/template/LegoEditView/index.tsx',
        LegoListView: 'src/template/LegoListView/index.tsx',
        LegoPermissionView: 'src/template/LegoPermissionView/index.tsx',
        LegoRenderView: 'src/template/LegoRenderView/index.tsx',
        LegoRender: 'src/template/LegoRender/index.tsx',
        LegoExportRecords: 'src/template/LegoExportRecords/index.tsx',
        LegoPublishExport: 'src/template/LegoPublishExport/index.tsx',
      },
      // target: 'es5',
      umdModuleName: 'biLegoSdk',
      // transformLodash: true,
      jsx: 'transform',
      // minify: 'terser',
      minify: false,
      externals: [
        'react',
        'react-dom',
        'prop-types',
        '@blmcp/peento-request',
        'dayjs',
        'antd',
        '@blmcp/ui',
        '@blmcp/peento-businessComponents',
        '@blmcp/ui-mobile',
        '@alilc/lowcode-editor-skeleton',
        '@alilc/lowcode-engine-ext',
        '@alilc/lowcode-engine',
        '@alilc/lowcode-utils',
        '@alifd/next',
      ],
      umdGlobals: {
        // react: 'React',
        // 'react-dom': 'ReactDOM',
        '@alilc/lowcode-editor-skeleton':
          'var window.AliLowCodeEngine.common.skeletonCabin',
        '@alilc/lowcode-engine-ext': 'var window.AliLowCodeEngineExt',
        '@alilc/lowcode-engine': 'var window.AliLowCodeEngine',
      },
      style: {
        inject: true,
        less: {
          lessOptions: {
            javascriptEnabled: true,
          },
        },
      },
      asset: {
        limit: 1024 * 1024,
        svgr: {
          exportType: 'named',
        },
      },
    });
  },
});
